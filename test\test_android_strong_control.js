const { fullIndexSearch } = require('../chat-agent.js');

async function testAndroidStrongControl() {
    console.log('Testing Android 强管控 search in 终端安全 path:');
    console.log('================================================');
    
    try {
        // 测试搜索"强管控"
        console.log('Test 1: Searching for "强管控" in "终端安全" path');
        const result1 = await fullIndexSearch('强管控', '终端安全');
        console.log('Results count:', result1.length);
        console.log('Results:', result1);
        console.log('');
        
        // 测试搜索"Android 强管控"
        console.log('Test 2: Searching for "Android 强管控" in "终端安全" path');
        const result2 = await fullIndexSearch('Android 强管控', '终端安全');
        console.log('Results count:', result2.length);
        console.log('Results:', result2);
        console.log('');
        
        // 测试搜索"Android"
        console.log('Test 3: Searching for "Android" in "终端安全" path');
        const result3 = await fullIndexSearch('Android', '终端安全');
        console.log('Results count:', result3.length);
        console.log('Results:', result3);
        console.log('');
        
        // 测试搜索"管控"
        console.log('Test 4: Searching for "管控" in "终端安全" path');
        const result4 = await fullIndexSearch('管控', '终端安全');
        console.log('Results count:', result4.length);
        console.log('Results:', result4);
        console.log('');
    } catch (error) {
        console.error('Error:', error.message);
    }
}

// Run the test
testAndroidStrongControl();
