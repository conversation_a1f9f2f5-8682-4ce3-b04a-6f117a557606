const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

// 获取终端安全数据库路径
const dbPath = path.join(__dirname, 'index_databases', 'full_index_终端安全.db');
console.log('Database path:', dbPath);

// 检查数据库文件是否存在
if (!fs.existsSync(dbPath)) {
    console.log('Database file does not exist');
    process.exit(1);
}

// 连接到数据库
const db = new Database(dbPath);
db.pragma('journal_mode = WAL');

// 检查FAQ 003文件是否在数据库中
console.log('\nChecking if 天擎_faq.txt.003 is indexed:');
const docResult = db.prepare('SELECT * FROM documents WHERE file_path = ?').get('天擎_faq.txt.003');
if (docResult) {
    console.log('Document found in documents table');
    console.log('File path:', docResult.file_path);
    console.log('File name:', docResult.file_name);
    console.log('Content preview:', docResult.content.substring(0, 100));
    
    // 检查是否包含"强管控"
    console.log('Contains "强管控":', docResult.content.includes('强管控'));
} else {
    console.log('Document not found in documents table');
}

// 检查FTS表
console.log('\nChecking FTS table:');
const ftsCount = db.prepare('SELECT COUNT(*) as count FROM fts_documents').get();
console.log('Total documents in FTS:', ftsCount.count);

// 尝试直接查询FAQ 003文件
console.log('\nChecking FTS for 天擎_faq.txt.003:');
const ftsResult = db.prepare('SELECT * FROM fts_documents WHERE file_path = ?').get('天擎_faq.txt.003');
if (ftsResult) {
    console.log('Document found in FTS table');
    console.log('File path:', ftsResult.file_path);
    console.log('File name:', ftsResult.file_name);
    console.log('Content preview:', ftsResult.content.substring(0, 100));
} else {
    console.log('Document not found in FTS table');
}

// 尝试查询包含"强管控"的文档
console.log('\nSearching for documents containing "强管控":');
const searchResults = db.prepare("SELECT file_path, snippet(fts_documents, 0, '>>>', '<<<', '...', 32) as snippet FROM fts_documents WHERE fts_documents MATCH '强管控'").all();
console.log('Found', searchResults.length, 'documents');
searchResults.forEach((result, index) => {
    console.log(`${index + 1}. ${result.file_path}`);
    console.log(`   Snippet: ${result.snippet}`);
});

// 尝试查询包含"Android"的文档
console.log('\nSearching for documents containing "Android":');
const androidResults = db.prepare("SELECT file_path, snippet(fts_documents, 0, '>>>', '<<<', '...', 32) as snippet FROM fts_documents WHERE fts_documents MATCH 'Android' LIMIT 5").all();
console.log('Found', androidResults.length, 'documents');
androidResults.forEach((result, index) => {
    console.log(`${index + 1}. ${result.file_path}`);
    console.log(`   Snippet: ${result.snippet}`);
});

db.close();
