{"name": "95015", "version": "1.0.0", "description": "这是一个基于Node.js的多轮会话智能体程序，支持命令行和Web界面两种交互方式，使用OpenAI兼容的API服务进行对话。", "main": "chat-agent.js", "scripts": {"test": "mocha --exit test/**/*.test.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.11.0", "better-sqlite3": "^12.2.0", "cors": "^2.8.5", "express": "^5.1.0", "jsdom": "^26.1.0", "node-fetch": "^2.7.0", "node-jieba": "^0.0.3", "puppeteer-core": "^24.15.0", "sqlite3": "^5.1.7", "xlsx": "^0.18.5"}, "devDependencies": {"chai": "^5.2.1", "mocha": "^11.7.1", "sinon": "^21.0.0", "supertest": "^7.1.4"}}