const fs = require('fs');
const path = require('path');
const { CNFullText } = require('./indexer'); // 假设 indexer.js 在同一目录

// 文件路径 - 根据实际情况修改
const filePath = 'd:/documents/终端安全/天擎_faq.txt.003';

async function main() {
  try {
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      console.error(`文件不存在: ${filePath}`);
      process.exit(1);
    }

    // 读取文件内容
    const content = fs.readFileSync(filePath, 'utf8');
    console.log(`✅ 成功读取文件: ${filePath}`);
    console.log(`文件大小: ${content.length} 字符`);
    console.log(`原文示例: ${content.substring(0, 100)}...`);

    // 创建 CNFullText 实例
    const searcher = new CNFullText({
      textField: 'content',
      idField: 'filePath',
      stopwords: [] // 可以添加停用词
    });

    // 构建索引 - 使用单个文件作为文档
    console.log('正在构建索引...');
    await searcher.build([{
      filePath: filePath,
      content: content,
      fileName: path.basename(filePath)
    }]);

    console.log('✅ 索引构建完成');
    console.log(`文档数量: ${searcher.docs.size}`);
    console.log(`词汇数量: ${searcher.inverted.size}`);

    // 保存索引到文件（可选）
    const indexPath = filePath + '.index.json';
    searcher.save(indexPath);
    console.log(`✅ 索引已保存到: ${indexPath}`);

    // 测试搜索功能
    console.log('\n🔍 测试搜索功能:');
    console.log('================================================');

    // 测试查询
    const testQueries = [
      'Android强管控',
      '强管控模式',
      '第三方应用',
      '管理员',
      'Android设备',
      '终端安全',
      'syslog',
      '天擎',
      '配置'
    ];

    for (const query of testQueries) {
      console.log(`\n🔍 搜索: "${query}"`);
      const results = searcher.search(query, { topK: 3, requireAll: false, snippetWidth: 100 });
      
      if (results.length > 0) {
        console.log(`  找到 ${results.length} 个结果:`);
        results.forEach((result, i) => {
          console.log(`  ${i + 1}. [得分: ${result.score.toFixed(4)}, 窗口: ${result.proximity}, 词频: ${result.tfTotal}]`);

          // 显示所有片段
          if (result.snippets && result.snippets.length > 0) {
            result.snippets.forEach((snippet, j) => {
              // 显示行号范围
              const [startLine, endLine] = snippet.lines;
              const lineRange = startLine === endLine ? `第${startLine}行` : `第${startLine}-${endLine}行`;

              // 限制片段长度以便更好地显示
              let displaySnippet = snippet.snippet;
              if (displaySnippet.length > 300) {
                displaySnippet = displaySnippet.substring(0, 300) + '...';
              }

              console.log(`     片段${j + 1} (${lineRange}): ${displaySnippet}`);
            });
          } else {
            console.log(`     片段: 无匹配内容`);
          }
        });
      } else {
        console.log('  未找到结果');
      }
    }

    // 分词效果分析
    console.log('\n📊 分词效果分析:');
    console.log('================================================');
    
    const analysisTexts = [
      'Android设备强管控模式',
      '天擎终端安全管理系统',
      '第三方应用安装限制',
      '管理员权限控制'
    ];

    // CNFullText 的 _tokenize 是同步的
    for (const text of analysisTexts) {
      const tokens = searcher._tokenize(text);
      console.log(`原文: "${text}"`);
      console.log(`分词: [${tokens.join(', ')}]`);
      console.log('');
    }

    // 文件编码检查
    console.log('\n📄 文件编码检查:');
    console.log('================================================');
    
    // 检查是否包含常见的中文字符
    const chineseCharRegex = /[\u4e00-\u9fa5]/;
    const containsChinese = chineseCharRegex.test(content);
    console.log(`包含中文字符: ${containsChinese}`);
    
    // 检查是否包含常见的乱码字符
    const garbledRegex = /[]/;
    const containsGarbled = garbledRegex.test(content);
    console.log(`包含乱码字符: ${containsGarbled}`);
    
    // 检查文件大小
    const stats = fs.statSync(filePath);
    console.log(`文件大小: ${stats.size} 字节`);
    
    if (containsGarbled) {
      console.warn('⚠️  警告: 文件可能包含乱码字符，建议检查文件编码');
    } else {
      console.log('✅ 文件编码检查通过');
    }

    // 性能测试
    console.log('\n⚡ 性能测试:');
    console.log('================================================');
    
    const testText = content.substring(0, Math.min(1000, content.length));
    const iterations = 100;
    
    // 测试传统搜索
    console.time('传统字符串搜索');
    for (let i = 0; i < iterations; i++) {
      testText.includes('强管控');
    }
    console.timeEnd('传统字符串搜索');
    
    // 测试分词搜索
    console.time('CNFullText分词搜索');
    for (let i = 0; i < iterations; i++) {
      searcher.search('强管控', { topK: 1 });
    }
    console.timeEnd('CNFullText分词搜索');

    console.log('\n✅ 所有测试完成！');

  } catch (error) {
    console.error('❌ 执行出错:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

main();
