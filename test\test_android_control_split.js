const { fullIndexSearch } = require('../chat-agent.js');

async function testAndroidControlSplit() {
    console.log('Testing Android 管控 split search in 终端安全 path:');
    console.log('================================================');
    
    try {
        // 测试搜索"终端安全"路径下的"Android 管控"（应该被视为两个独立关键词）
        console.log('Test: Searching for "Android 管控" in "终端安全" path');
        console.log('This should be treated as two separate keywords: "Android" and "管控"');
        const result = await fullIndexSearch('Android 管控', '终端安全');
        console.log('Results count:', result.length);
        console.log('Results:');
        result.forEach((item, index) => {
            console.log(`${index + 1}. ${item}`);
        });
        console.log('');
        
        // 检查是否找到了天擎_faq.txt.003文件
        const foundFaq003 = result.some(item => item.includes('天擎_faq.txt.003'));
        console.log('Found 天擎_faq.txt.003:', foundFaq003 ? 'YES' : 'NO');
        console.log('');
        
        // 测试搜索"强管控"
        console.log('Test: Searching for "强管控" in "终端安全" path');
        const result2 = await fullIndexSearch('强管控', '终端安全');
        console.log('Results count:', result2.length);
        console.log('Results:');
        result2.forEach((item, index) => {
            console.log(`${index + 1}. ${item}`);
        });
        console.log('');
        
        // 检查是否找到了包含"强管控"的文件
        if (result2.length > 0) {
            console.log('Files containing "强管控":');
            result2.forEach((item, index) => {
                const lines = item.split('\n');
                const fileName = lines[0].replace('文件: ', '');
                console.log(`  ${index + 1}. ${fileName}`);
            });
        }
    } catch (error) {
        console.error('Error:', error.message);
    }
}

// Run the test
testAndroidControlSplit();
