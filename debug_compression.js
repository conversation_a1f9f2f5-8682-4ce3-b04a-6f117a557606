const fs = require('fs');
const zlib = require('zlib');
const path = require('path');

// 模拟索引保存过程
function testCompression() {
  // 创建测试数据
  const testData = {
    message: "这是一个测试数据，用于检查压缩和解压过程中的长度问题。",
    numbers: [1, 2, 3, 4, 5],
    nested: {
      key1: "value1",
      key2: "value2"
    }
  };

  // 序列化为JSON字符串
  const jsonStr = JSON.stringify(testData);
  const jsonBuffer = Buffer.from(jsonStr, 'utf8');
  
  console.log(`原始JSON字符串长度: ${jsonStr.length}`);
  console.log(`原始JSON缓冲区长度: ${jsonBuffer.length}`);

  // 使用gzip压缩
  const compressed = zlib.gzipSync(jsonBuffer, { level: 6 });
  console.log(`压缩后长度: ${compressed.length}`);

  // 保存到文件
  const header = Buffer.from('CNFT', 'ascii');
  const version = Buffer.alloc(4);
  version.writeUInt32LE(2, 0);
  const originalLength = Buffer.alloc(4);
  originalLength.writeUInt32LE(jsonBuffer.length, 0);
  const compressedLength = Buffer.alloc(4);
  compressedLength.writeUInt32LE(compressed.length, 0);

  const finalBuffer = Buffer.concat([header, version, originalLength, compressedLength, compressed]);
  
  // 保存到文件
  const testFile = './test_compression.bin';
  fs.writeFileSync(testFile, finalBuffer);
  console.log(`文件已保存到: ${testFile}`);
  console.log(`文件总长度: ${finalBuffer.length}`);

  // 模拟加载过程
  const buffer = fs.readFileSync(testFile);
  
  // 读取文件头信息
  const headerCheck = buffer.subarray(0, 4).toString('ascii');
  const versionCheck = buffer.readUInt32LE(4);
  const originalLengthCheck = buffer.readUInt32LE(8);
  const compressedLengthCheck = buffer.readUInt32LE(12);
  const compressedData = buffer.subarray(16);
  
  console.log(`文件头: ${headerCheck}`);
  console.log(`版本号: ${versionCheck}`);
  console.log(`记录的原始长度: ${originalLengthCheck}`);
  console.log(`记录的压缩后长度: ${compressedLengthCheck}`);
  console.log(`实际压缩数据长度: ${compressedData.length}`);
  
  // 解压数据
  const decompressed = zlib.gunzipSync(compressedData);
  const decompressedStr = decompressed.toString('utf8');
  
  console.log(`解压后长度: ${decompressedStr.length}`);
  
  if (decompressedStr.length !== originalLengthCheck) {
    console.log(`长度不匹配! 期望: ${originalLengthCheck}, 实际: ${decompressedStr.length}`);
  } else {
    console.log(`长度匹配!`);
  }
  
  // 验证数据完整性
  try {
    const parsed = JSON.parse(decompressedStr);
    console.log(`数据解析成功`);
    console.log(`数据内容:`, JSON.stringify(parsed, null, 2));
  } catch (error) {
    console.error(`数据解析失败: ${error.message}`);
  }
  
  // 清理测试文件
  fs.unlinkSync(testFile);
  console.log(`测试文件已删除`);
}

testCompression();
