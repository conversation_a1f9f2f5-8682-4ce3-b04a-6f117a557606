const Database = require('better-sqlite3');

// 创建一个临时数据库来测试分词
const db = new Database(':memory:');
db.exec(`
    CREATE VIRTUAL TABLE test_fts USING fts5(
        content,
        tokenize='unicode61'
    );
`);

// 插入测试数据
db.exec(`INSERT INTO test_fts (content) VALUES ('Android 强管控')`);

// 测试不同的查询
console.log('Testing FTS tokenization for "强管控":');
console.log('=====================================');

// 查询1: 直接查询"强管控"
const result1 = db.prepare(`SELECT * FROM test_fts WHERE test_fts MATCH '强管控'`).all();
console.log('Query: "强管控"');
console.log('Results:', result1.length > 0 ? 'Found' : 'Not found');
console.log('');

// 查询2: 分别查询每个字符
const result2 = db.prepare(`SELECT * FROM test_fts WHERE test_fts MATCH '强 AND 管 AND 控'`).all();
console.log('Query: "强 AND 管 AND 控"');
console.log('Results:', result2.length > 0 ? 'Found' : 'Not found');
console.log('');

// 查询3: 查询"强"
const result3 = db.prepare(`SELECT * FROM test_fts WHERE test_fts MATCH '强'`).all();
console.log('Query: "强"');
console.log('Results:', result3.length > 0 ? 'Found' : 'Not found');
console.log('');

// 查询4: 查询"管"
const result4 = db.prepare(`SELECT * FROM test_fts WHERE test_fts MATCH '管'`).all();
console.log('Query: "管"');
console.log('Results:', result4.length > 0 ? 'Found' : 'Not found');
console.log('');

// 查询5: 查询"控"
const result5 = db.prepare(`SELECT * FROM test_fts WHERE test_fts MATCH '控'`).all();
console.log('Query: "控"');
console.log('Results:', result5.length > 0 ? 'Found' : 'Not found');
console.log('');

// 查询6: 查询"Android"
const result6 = db.prepare(`SELECT * FROM test_fts WHERE test_fts MATCH 'Android'`).all();
console.log('Query: "Android"');
console.log('Results:', result6.length > 0 ? 'Found' : 'Not found');
console.log('');

// 查询7: 查询"Android 强管控"
const result7 = db.prepare(`SELECT * FROM test_fts WHERE test_fts MATCH '"Android 强管控"'`).all();
console.log('Query: "\\"Android 强管控\\""');
console.log('Results:', result7.length > 0 ? 'Found' : 'Not found');
console.log('');

// 查询8: 查询"Android"和"强管控"
const result8 = db.prepare(`SELECT * FROM test_fts WHERE test_fts MATCH 'Android AND 强管控'`).all();
console.log('Query: "Android AND 强管控"');
console.log('Results:', result8.length > 0 ? 'Found' : 'Not found');
console.log('');

db.close();
