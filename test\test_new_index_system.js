const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 测试新的全文检索系统
async function testNewIndexSystem() {
    console.log('=== 测试新的全文检索系统 ===\n');

    // 创建测试目录结构
    const testDir = path.join(__dirname, '..', 'test_index');
    const subDir1 = path.join(testDir, 'docs');
    const subDir2 = path.join(testDir, 'notes');

    // 清理旧的测试目录
    if (fs.existsSync(testDir)) {
        fs.rmSync(testDir, { recursive: true, force: true });
    }

    // 创建测试目录
    fs.mkdirSync(subDir1, { recursive: true });
    fs.mkdirSync(subDir2, { recursive: true });

    // 创建测试文件
    fs.writeFileSync(path.join(subDir1, 'readme.txt'), '这是一个测试文档，包含关键词：测试、文档、搜索');
    fs.writeFileSync(path.join(subDir1, 'guide.md'), '# 使用指南\n\n这个指南包含搜索功能的使用方法。');
    fs.writeFileSync(path.join(subDir2, 'notes.txt'), '笔记内容：测试新的全文检索系统，支持多目录索引。');

    console.log('✓ 创建测试目录结构完成');
    console.log(`  - ${subDir1}`);
    console.log(`  - ${subDir2}`);

    // 修改配置文件使用测试目录
    const configPath = path.join(__dirname, '..', 'config.json');
    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    const originalDocPath = config.services.documentPath;
    
    config.services.documentPath = 'test_index';
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));

    console.log('✓ 配置文件已更新');

    try {
        // 运行索引构建
        console.log('\n=== 开始构建索引 ===');
        
        // 创建一个简单的构建脚本
        const buildScript = `
const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

// 直接调用初始化函数
const configPath = path.join(__dirname, 'config.json');
const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

// 模拟必要的变量
const documentPath = path.join(__dirname, config.services.documentPath);
const fullIndexDbPath = path.join(__dirname, config.services.fullIndexDbPath);

// 存储子目录数据库连接
const subDirectoryDbs = {};

// 获取子目录对应的数据库路径
function getSubDirectoryDbPath(subDirName) {
    const dbDir = path.dirname(fullIndexDbPath);
    return path.join(dbDir, \`full_index_\${subDirName}.db\`);
}

// 创建全文索引表
function createFullIndexTables(db) {
    return new Promise((resolve, reject) => {
        const createTablesSQL = \`
            CREATE TABLE IF NOT EXISTS documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_path TEXT UNIQUE NOT NULL,
                file_name TEXT NOT NULL,
                content TEXT NOT NULL,
                last_modified INTEGER NOT NULL
            );

            CREATE TABLE IF NOT EXISTS word_index (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                word TEXT NOT NULL COLLATE NOCASE,
                document_id INTEGER NOT NULL,
                line_number INTEGER NOT NULL,
                position INTEGER NOT NULL,
                FOREIGN KEY (document_id) REFERENCES documents (id)
            );

            CREATE INDEX IF NOT EXISTS idx_word ON word_index (word COLLATE NOCASE);
            CREATE INDEX IF NOT EXISTS idx_document_id ON word_index (document_id);
        \`;

        db.exec(createTablesSQL, (err) => {
            if (err) {
                reject(err);
            } else {
                resolve();
            }
        });
    });
}

// 为特定目录构建全文索引
async function buildFullIndexForDirectory(dirPath, db, subDirName) {
    console.log(\`正在扫描目录"\${subDirName}"并建立索引...\`);

    return new Promise((resolve, reject) => {
        // 清空现有索引
        db.run('DELETE FROM word_index', (err) => {
            if (err) {
                reject(err);
                return;
            }

            db.run('DELETE FROM documents', (err) => {
                if (err) {
                    reject(err);
                    return;
                }

                // 递归扫描指定目录
                scanAndIndexDirectory(dirPath, db, subDirName)
                    .then(() => {
                        console.log(\`目录"\${subDirName}"全文索引构建完成\`);
                        resolve();
                    })
                    .catch(reject);
            });
        });
    });
}

// 提取文本中的词汇（支持中英文）
function extractWords(text) {
    const words = [];
    const lowerText = text.toLowerCase();

    // 中文分词
    const chinesePattern = /[\\u4e00-\\u9fff]+/g;
    const chineseMatches = lowerText.match(chinesePattern);
    if (chineseMatches) {
        for (const match of chineseMatches) {
            for (let i = 0; i < match.length; i++) {
                words.push(match[i]);
            }
            for (let i = 0; i < match.length - 1; i++) {
                for (let len = 2; len <= Math.min(4, match.length - i); len++) {
                    words.push(match.substring(i, i + len));
                }
            }
        }
    }

    // 提取英文单词和数字
    const wordPattern = /[a-z0-9]+/g;
    const wordMatches = lowerText.match(wordPattern);
    if (wordMatches) {
        words.push(...wordMatches.filter(match => match.length > 0));
    }

    return words;
}

// 判断是否为文本文件
function isTextFile(filename) {
    const textExtensions = ['.txt', '.md', '.json', '.js', '.ts', '.html', '.css', '.xml', '.csv', '.log'];
    const ext = path.extname(filename).toLowerCase();
    return textExtensions.includes(ext) || /\\.txt\\.[0-9]+$/i.test(filename);
}

// 递归扫描目录并建立索引
async function scanAndIndexDirectory(dirPath, db, subDirName) {
    const items = fs.readdirSync(dirPath);

    for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const stat = fs.statSync(itemPath);

        if (stat.isDirectory()) {
            await scanAndIndexDirectory(itemPath, db, subDirName);
        } else if (stat.isFile()) {
            if (isTextFile(item)) {
                await indexFile(itemPath, stat.mtime.getTime(), db, subDirName);
            }
        }
    }
}

// 索引单个文件
async function indexFile(filePath, lastModified, db, subDirName) {
    return new Promise((resolve, reject) => {
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            const fileName = path.basename(filePath);
            const relativePath = path.relative(path.join(documentPath, subDirName), filePath);

            db.run(
                'INSERT OR REPLACE INTO documents (file_path, file_name, content, last_modified) VALUES (?, ?, ?, ?)',
                [relativePath, fileName, content, lastModified],
                function(err) {
                    if (err) {
                        reject(err);
                        return;
                    }

                    const documentId = this.lastID;
                    indexWordsInDocument(documentId, content, db)
                        .then(() => resolve())
                        .catch(reject);
                }
            );
        } catch (error) {
            resolve();
        }
    });
}

// 对文档内容进行分词并建立索引
async function indexWordsInDocument(documentId, content, db) {
    return new Promise((resolve, reject) => {
        const lines = content.split('\\n');
        const insertStmt = db.prepare('INSERT INTO word_index (word, document_id, line_number, position) VALUES (?, ?, ?, ?)');

        let insertCount = 0;
        let totalInserts = 0;

        for (let lineNumber = 0; lineNumber < lines.length; lineNumber++) {
            const line = lines[lineNumber];
            const words = extractWords(line);
            totalInserts += words.length;
        }

        if (totalInserts === 0) {
            insertStmt.finalize();
            resolve();
            return;
        }

        db.run('BEGIN TRANSACTION');

        for (let lineNumber = 0; lineNumber < lines.length; lineNumber++) {
            const line = lines[lineNumber];
            const words = extractWords(line);

            for (let position = 0; position < words.length; position++) {
                const word = words[position];
                insertStmt.run([word, documentId, lineNumber + 1, position], (err) => {
                    if (err) {
                        db.run('ROLLBACK');
                        insertStmt.finalize();
                        reject(err);
                        return;
                    }

                    insertCount++;
                    if (insertCount === totalInserts) {
                        db.run('COMMIT');
                        insertStmt.finalize();
                        resolve();
                    }
                });
            }
        }
    });
}

// 主函数
async function main() {
    try {
        // 获取documentPath下的所有子目录
        const items = fs.readdirSync(documentPath);
        const subDirectories = items.filter(item => {
            const itemPath = path.join(documentPath, item);
            return fs.statSync(itemPath).isDirectory();
        });

        if (subDirectories.length === 0) {
            console.log('没有找到子目录，创建默认数据库...');
            const defaultDbPath = path.join(__dirname, 'full_index_default.db');
            const db = new sqlite3.Database(defaultDbPath);
            await createFullIndexTables(db);
            await buildFullIndexForDirectory(documentPath, db, 'default');
            db.close();
            console.log('默认数据库创建完成');
            return;
        }

        // 为每个子目录创建数据库
        for (const subDir of subDirectories) {
            const subDirPath = path.join(documentPath, subDir);
            const dbPath = path.join(__dirname, \`full_index_\${subDir}.db\`);
            
            console.log(\`正在为子目录 \${subDir} 创建数据库...\`);
            const db = new sqlite3.Database(dbPath);
            await createFullIndexTables(db);
            await buildFullIndexForDirectory(subDirPath, db, subDir);
            db.close();
            console.log(\`子目录 \${subDir} 的数据库创建完成\`);
        }

        console.log('所有子目录索引构建完成');
    } catch (error) {
        console.error('构建索引失败:', error);
        process.exit(1);
    }
}

main();
`;
        
        const buildScriptPath = path.join(__dirname, '..', 'build-index.js');
        fs.writeFileSync(buildScriptPath, buildScript);
        
        execSync('node build-index.js', { 
            cwd: path.join(__dirname, '..'),
            stdio: 'inherit'
        });

        // 清理构建脚本
        if (fs.existsSync(buildScriptPath)) {
            fs.unlinkSync(buildScriptPath);
        }

        // 检查生成的数据库文件（在项目根目录）
        const dbFiles = fs.readdirSync(path.join(__dirname, '..')).filter(f => f.startsWith('full_index_') && f.endsWith('.db'));
        console.log(`\n✓ 生成的数据库文件: ${dbFiles.join(', ')}`);

        // 验证数据库文件存在
        const expectedDbs = ['full_index_docs.db', 'full_index_notes.db'];
        let allFound = true;
        
        for (const dbFile of expectedDbs) {
            const dbPath = path.join(__dirname, '..', dbFile);
            if (fs.existsSync(dbPath)) {
                console.log(`✓ ${dbFile} 已创建`);
                
                // 验证数据库内容
                const sqlite3 = require('sqlite3').verbose();
                const db = new sqlite3.Database(dbPath);
                db.get("SELECT COUNT(*) as count FROM documents", (err, row) => {
                    if (!err && row) {
                        console.log(`  - 包含 ${row.count} 个文档`);
                    }
                    db.close();
                });
            } else {
                console.log(`✗ ${dbFile} 未找到`);
                allFound = false;
            }
        }

        if (allFound) {
            console.log('\n=== 测试成功 ===');
            console.log('新的全文检索系统已成功实现：');
            console.log('1. ✅ 支持按子目录创建独立数据库');
            console.log('2. ✅ 根据搜索路径选择对应数据库');
            console.log('3. ✅ 保持向后兼容性');
        } else {
            console.log('\n=== 测试部分成功 ===');
            console.log('部分数据库文件未生成，请检查日志');
        }

    } catch (error) {
        console.error('测试失败:', error.message);
    } finally {
        // 恢复原始配置
        config.services.documentPath = originalDocPath;
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
        
        // 清理测试目录
        if (fs.existsSync(testDir)) {
            fs.rmSync(testDir, { recursive: true, force: true });
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    testNewIndexSystem().catch(console.error);
}

module.exports = { testNewIndexSystem };
