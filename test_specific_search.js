const { fullIndexSearch } = require('./chat-agent.js');

async function testSpecificSearch() {
    console.log('Testing specific search scenarios:');
    console.log('================================');
    
    // 测试1: 搜索"终端安全"路径下的"Android 强管控"
    console.log('Test 1: Searching for "Android 强管控" in "终端安全" path');
    try {
        const result1 = await fullIndexSearch('Android 强管控', '终端安全');
        console.log('Results:', result1);
    } catch (error) {
        console.log('Error:', error.message);
    }
    console.log('');
    
    // 测试2: 搜索"终端安全"路径下的"强管控"
    console.log('Test 2: Searching for "强管控" in "终端安全" path');
    try {
        const result2 = await fullIndexSearch('强管控', '终端安全');
        console.log('Results:', result2);
    } catch (error) {
        console.log('Error:', error.message);
    }
    console.log('');
    
    // 测试3: 搜索"终端安全"路径下的"Android"
    console.log('Test 3: Searching for "Android" in "终端安全" path');
    try {
        const result3 = await fullIndexSearch('Android', '终端安全');
        console.log('Results:', result3);
    } catch (error) {
        console.log('Error:', error.message);
    }
    console.log('');
}

// Initialize the full index before running tests
require('./chat-agent.js').initializeFullIndex().then(() => {
    setTimeout(() => {
        testSpecificSearch();
    }, 1000);
}).catch(error => {
    console.error('Failed to initialize full index:', error);
});
