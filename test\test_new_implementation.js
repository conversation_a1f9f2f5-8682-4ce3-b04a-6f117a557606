const { executeFullIndexSearch, initializeFullIndex } = require('../chat-agent.js');

// 导出函数供测试使用
module.exports = {
    executeFullIndexSearch,
    initializeFullIndex
};

async function testNewImplementation() {
    console.log('测试新的全文索引实现...\n');

    // 初始化全文索引引擎
    try {
        await initializeFullIndex();
        console.log('全文索引引擎初始化完成\n');
    } catch (error) {
        console.error('全文索引引擎初始化失败:', error);
        process.exit(1);
    }
    
    // 测试用例：搜索"终端安全"路径下的"Android 管控"
    console.log('测试用例：搜索"终端安全"路径下的"Android 管控"');
    const result = await executeFullIndexSearch({
        keywords: ["Android 管控"],
        path: "终端安全"
    });
    console.log('测试结果:', result);
    console.log('----------------------------------------\n');
    
    // 测试用例：搜索"终端安全"路径下的"管控"
    console.log('测试用例：搜索"终端安全"路径下的"管控"');
    const result2 = await executeFullIndexSearch({
        keywords: ["管控"],
        path: "终端安全"
    });
    console.log('测试结果:', result2);
    console.log('----------------------------------------\n');
    
    console.log('测试完成');
}

// 如果直接运行此脚本
if (require.main === module) {
    testNewImplementation().catch(err => {
        console.error('测试失败:', err);
    });
}
